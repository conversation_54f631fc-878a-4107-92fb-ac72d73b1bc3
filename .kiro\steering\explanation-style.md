---
inclusion: always
---

# 技术解释风格指南

当用户询问技术概念、工作原理或实现细节时，请按照以下结构和风格进行回答：

## 语言风格要求

- **通俗易懂**：避免过度技术化的表达，用日常语言解释复杂概念
- **循序渐进**：从简单到复杂，逐步深入
- **实用导向**：结合实际应用场景和用例
- **视觉化**：优先使用图表、代码块等可视化方式

## Mermaid图表使用指南

根据内容类型选择合适的图表：

- **流程图**：用于解释算法步骤、决策流程
- **时序图**：用于解释通信协议、函数调用顺序
- **架构图**：用于解释系统结构、模块关系
- **状态图**：用于解释状态机、生命周期

## 代码示例要求

- 使用完整、可运行的代码片段
- 添加详细的中文注释
- 突出关键代码行
- 提供多个层次的示例（从简单到复杂）

## 特别注意

- 对于嵌入式/STM32相关问题，要结合硬件特性解释
- 对于通信协议问题，要包含时序图和数据格式
- 对于算法问题，要包含流程图和复杂度分析
- 始终从用户的角度思考，什么样的解释最容易理解